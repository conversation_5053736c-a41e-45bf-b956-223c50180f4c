# WAIC Demo - Defake深伪检测防御演示项目

## 项目概述

本项目为人工智能大会（WAIC）演示项目，目标是展现Defake深伪检测防御AIGC生成人脸的效果。通过现场互动演示，让观众直观了解深伪技术的生成过程以及相应的检测防御能力。

## 整体需求设计

### 1. 展示场景定义

**展会现场设置：**
- 一台大屏幕显示器（用于展示整个演示过程）
- 现场提供预装演示APP的手机供用户采集人脸
- 现场工作人员引导用户体验

**演示流程：**
1. 系统启动，APP自动连接到大屏系统
2. 工作人员点击"开启拍照上传"按钮
3. 用户使用Android APP拍摄人脸并上传
4. 大屏幕实时显示处理进度和状态同步
5. 展示LivePortrait生成的人脸驱动视频
6. 展示Defake检测结果
7. 展示AI大模型的可解释性分析
8. 演示完成后自动重置，准备下一轮

### 2. 功能模块需求

#### 2.1 Android APP（界面设计为主）
**主要功能：**
- 简洁的拍照界面设计
- 照片预览和确认功能
- 文件上传进度显示
- 状态提示和用户引导

**UI设计要求：**
- Material Design设计风格
- 大按钮设计，适合展会现场操作
- 清晰的状态反馈
- 友好的用户提示文案

#### 2.2 Web前端展示页面（大屏幕显示）
**主要功能：**
- 实时状态显示（等待上传、处理中、完成）
- 原始照片展示区域
- 合成视频播放区域
- Defake检测结果展示
- AI大模型解释文本展示

**UI设计要求：**
- 适配大屏幕显示器的布局
- 科技感强的视觉设计
- 清晰的信息层次结构
- 流畅的状态转换动画

#### 2.3 后端处理系统
**核心功能：**
- 照片接收和预处理
- LivePortrait人脸驱动调用
- Defake检测算法集成
- 大模型API调用
- 实时状态推送

### 3. 技术实现范围

**当前阶段实现：**
- 步骤1-3：用户上传 → LivePortrait人脸驱动 → 效果展示
- 使用KwaiVGI的LivePortrait项目代码

**后续扩展：**
- 步骤4-6：Defake检测 → 结果展示 → AI解释分析

### 4. 用户体验流程

#### 4.1 用户操作流程
1. **系统准备阶段** - 页面加载，APP自动连接到大屏系统
2. **引导阶段** - 工作人员介绍演示内容
3. **开启阶段** - 工作人员点击"开启拍照上传"按钮
4. **拍照阶段** - 用户使用APP拍摄自拍照（APP状态同步显示）
5. **上传阶段** - 照片上传，左侧照片区域实时显示状态
6. **处理阶段** - 大屏显示6步处理进度时间线
7. **展示阶段** - 中间区域对比显示原图与合成视频
8. **检测阶段** - 右侧展示深伪检测结果
9. **分析阶段** - AI详细分析结果滚动显示
10. **重置阶段** - 演示完成后自动重置，准备下一轮

#### 4.2 异常处理流程
- 网络连接异常处理
- 人脸检测失败处理
- 模型处理超时处理
- 系统错误友好提示

### 5. 性能和体验要求

**响应时间要求：**
- 照片上传：< 5秒
- LivePortrait处理：< 30秒
- Defake检测：< 15秒
- 整体流程：< 60秒

**用户体验要求：**
- 界面操作简单直观
- 状态反馈及时准确
- 错误提示友好明确
- 视觉效果震撼有趣

### 6. 展示效果目标

**技术展示目标：**
- 展现LivePortrait人脸驱动技术的先进性
- 证明Defake检测技术的有效性
- 体现AI技术在内容安全领域的应用价值

**观众体验目标：**
- 让观众直观感受深伪技术的真实效果
- 提升对AI内容安全技术的认知
- 增强对技术发展的信心和期待

## UI设计方案

### 1. Android APP界面设计

#### 主界面布局
```
┌─────────────────────────┐
│  SSID - Defake演示      │
│    深伪检测防御         │
├─────────────────────────┤
│                         │
│    [相机预览区域]        │
│                         │
│                         │
├─────────────────────────┤
│     [拍照按钮]          │
│                         │
│  [重新拍照] [确认上传]   │
│                         │
│   上传进度: ████░░ 80%   │
└─────────────────────────┘
```

#### 界面状态流转
1. **初始状态** - 显示相机预览，拍照按钮
2. **拍照完成** - 显示照片预览，重拍/确认按钮
3. **上传中** - 显示上传进度条，禁用操作
4. **上传完成** - 显示成功提示，引导用户关注大屏

#### 设计特点
- Material Design设计风格
- 大按钮设计，适合展会现场操作
- 清晰的状态反馈和用户引导
- 友好的错误提示和异常处理

### 2. 大屏幕展示界面设计

#### 整体布局架构
```
┌─────────────────────────────────────────────────────────────────────────┐
│                    SSID - Defake深伪检测防御演示                        │
│        [系统状态: 系统就绪] [APP状态: 已连接] [当前时间: 14:32:15]        │
├─────────────────┬─────────────────────────────┬─────────────────────────┤
│   左侧进度栏    │        中央对比展示区        │      右侧结果栏         │
│    (25%)       │          (42%)             │        (33%)           │
│                │                            │                        │
│ 处理进度时间线   │  ┌──────────┐ → ┌──────────┐ │ 深伪检测结果            │
│ ✅ APP连接成功   │  │ 原始照片  │   │ 合成视频  │ │ ┌─────────────────┐   │
│ 🔄 等待用户拍照  │  │240×426px │   │240×426px │ │ │   95.7% 合成    │   │
│ ⏳ 人脸检测     │  │          │   │          │ │ │   高风险内容     │   │
│ ⏳ 视频生成     │  └──────────┘   └──────────┘ │ └─────────────────┘   │
│ ⏳ 深伪检测     │                            │                        │
│ ⏳ AI分析      │   [开启拍照上传] [重置演示]   │ 大模型AI分析 (扩展)      │
│                │                            │ ┌─────────────────────┐ │
│                │                            │ │ 基于深度学习模型的   │ │
│                │                            │ │ 综合分析结果：      │ │
│                │                            │ │                    │ │
│                │                            │ │ 1. 面部特征一致性... │ │
│                │                            │ │ 2. 光照与阴影...    │ │
│                │                            │ │ 3. 边缘模糊度...    │ │
│                │                            │ │ [详细分析内容]      │ │
│                │                            │ └─────────────────────┘ │
└─────────────────┴─────────────────────────────┴─────────────────────────┘
```

#### 核心设计特点

**视觉风格：**
- 深色科技风格背景，适合大屏幕展示
- 蓝色到青色的科技感渐变色彩
- 发光效果和脉冲动画增强视觉冲击
- 卡片式布局，层次分明

**视频展示区域：**
- 手机竖屏比例：360px × 640px (9:16)
- 居中显示，突出核心内容
- 扫描线动画效果，增强科技感
- 支持视频播放控制和下载功能

**状态反馈系统：**
- 实时状态指示器（等待/处理中/完成/错误）
- 进度条动画显示处理进度
- 时间线展示完整流程步骤
- 状态变化的平滑过渡动画

#### 功能模块详细设计

**左侧栏 - 处理进度时间线：**
- 6步完整流程：APP连接 → 用户拍照 → 人脸检测 → 视频生成 → 深伪检测 → AI分析
- 实时状态指示：等待中/进行中/已完成，配合颜色和动画
- 时间戳记录：每个步骤的完成时间
- 进度条显示：视频生成步骤的详细进度

**中央区域 - 原图与视频对比展示：**
- 左右并排布局：原始照片(240×426px) ↔ 合成视频(240×426px)
- 状态同步显示：照片区域显示拍照相关状态，视频区域显示生成相关状态
- 视觉对比效果：箭头指示和"AI生成"标签
- 控制按钮：开启拍照上传、重置演示、下载视频

**右侧栏 - 检测结果与AI分析：**
- 深伪检测结果：置信度评分和风险等级判断
- 大模型AI分析：扩展显示区域，详细技术分析内容
- 滚动文本显示：打字机效果逐步显示分析结果
- 多维度分析：面部特征、光照一致性、边缘模糊等8个维度

### 3. 交互设计规范

#### 动画效果
- **状态转换**：平滑的淡入淡出效果
- **进度指示**：脉冲动画和进度条更新
- **加载状态**：旋转图标和呼吸灯效果
- **文本显示**：打字机效果逐字显示AI分析

#### 响应式设计
- 适配不同尺寸的展示屏幕
- 保持关键信息的可读性
- 合理的字体大小和间距
- 触摸友好的按钮尺寸

#### 错误处理
- 网络连接异常的友好提示
- 人脸检测失败的重试机制
- 处理超时的状态显示
- 系统错误的用户引导

### 4. 技术实现细节

#### 前端技术栈
- HTML5 + CSS3 + JavaScript
- Tailwind CSS响应式框架
- Font Awesome图标库
- WebSocket实时通信

#### 样式特性
- CSS Grid布局系统
- CSS动画和过渡效果
- 自定义CSS变量管理主题
- 媒体查询适配不同屏幕

#### 交互逻辑
- 事件驱动的状态管理
- 异步任务的进度跟踪
- 实时数据更新机制
- 用户操作的反馈系统

### 5. 部署和使用说明

#### 文件结构
```
WAIC_Demo/
├── README.md                 # 项目文档
├── ui-design.html           # 综合UI设计展示页面
├── display-screen.html      # 大屏幕展示界面
└── (其他项目文件)
```

#### 使用方式
1. **开发预览**：直接在浏览器中打开HTML文件
2. **展会部署**：将文件部署到Web服务器
3. **大屏展示**：全屏模式打开display-screen.html
4. **移动端测试**：使用ui-design.html查看整体设计

#### 浏览器兼容性
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

### 6. 触发机制与状态同步设计

#### 6.1 完整演示流程控制

**系统启动阶段：**
- 页面加载完成，显示"系统就绪"状态
- 自动模拟APP连接过程（2秒）
- APP连接成功后，左侧照片区域显示"APP已连接，等待开启拍照功能"
- "开启拍照上传"按钮变为可用状态

**拍照准备阶段：**
- 工作人员点击"开启拍照上传"按钮
- 左侧照片区域显示"拍照功能已开启，请使用手机APP拍照"（绿色脉冲动画）
- APP端接收信号，允许用户拍照上传
- 系统状态更新为"等待用户拍照上传"

**照片上传阶段：**
- 用户在APP中拍照并上传
- 左侧照片区域实时显示上传状态："照片上传成功，开始处理..."
- 进度时间线更新："用户拍照"步骤标记为完成
- 可选择显示实际上传的照片替换占位符

**自动处理阶段：**
- 系统自动执行：人脸检测 → 视频生成 → 深伪检测 → AI分析
- 左侧进度时间线实时更新每个步骤的状态和完成时间
- 右侧视频区域显示生成进度和扫描线动画
- 右侧结果区域逐步显示检测结果和AI分析

**演示完成阶段：**
- 所有步骤完成，系统状态更新为"演示完成"
- 显示下载按钮，允许下载生成的视频
- 10秒后自动重置到初始状态，准备下一轮演示

#### 6.2 状态同步机制

**大屏端状态指示器：**
- 系统状态：系统就绪 / 等待用户拍照上传 / 演示进行中 / 演示完成
- APP连接状态：未连接 / 已连接（配合图标颜色变化）
- 实时时间显示：当前系统时间

**APP端状态控制（设计概念）：**
- 初始状态：拍照功能禁用，显示"等待工作人员开启拍照功能"
- 开启状态：收到大屏信号，拍照按钮激活，显示"请拍摄您的照片"
- 上传状态：照片上传中，显示进度条
- 处理状态：上传完成后禁用拍照，显示"处理中，请关注大屏展示"
- 重置状态：演示完成后重新允许拍照

**状态显示位置逻辑：**
- 照片相关状态 → 显示在左侧照片区域（APP连接、拍照开启、上传成功）
- 视频相关状态 → 显示在中央视频区域（生成进度、完成状态）
- 检测相关状态 → 显示在右侧结果区域（检测进度、分析结果）

#### 6.3 工作人员操作界面

**控制按钮功能：**
- "开始新演示"：一键启动完整演示流程，自动开启拍照功能
- "手动开启拍照"：备用按钮，默认隐藏，特殊情况下手动控制
- "下载"：演示完成后显示，提供视频下载功能

**监控信息显示：**
- 6步进度时间线：完整展示从APP连接到AI分析的全流程
- 实时时间戳：记录每个关键步骤的完成时间
- 系统状态指示：当前演示阶段和APP连接状态

#### 6.4 展会现场使用流程

**标准操作流程：**
1. 工作人员向观众介绍演示内容和操作方式
2. 工作人员点击"开启拍照上传"按钮
3. 观众使用提供的手机APP拍摄自拍照
4. 系统自动处理并在大屏上展示完整流程
5. 工作人员结合大屏内容解释技术原理和检测结果
6. 演示完成后系统自动重置，准备服务下一位观众

**异常处理机制：**
- 技术故障：随时可点击"开始新演示"按钮重置并启动新流程
- 用户操作问题：清晰的状态提示帮助引导用户正确操作
- 超时处理：长时间无操作自动重置，避免界面停滞
- 流程简化：一键操作减少人为错误，提高演示效率

### 7. 设计迭代记录

#### v1.0 - 初始设计
- 基础三栏布局
- 手机竖屏视频显示
- 基本状态反馈

#### v1.1 - 优化调整
- 移除系统状态显示
- 标题修改为SSID品牌
- 简化检测结果显示

#### v1.2 - 功能完善
- 新增大模型AI分析输出框
- 优化右侧栏布局
- 移除技术细节显示

#### v1.3 - 布局优化
- 整合左侧栏：只保留处理进度时间线
- 中央区域：原图与视频并排对比显示
- 右侧栏：扩大AI分析显示区域

#### v1.4 - 触发机制完善
- 新增完整的演示流程控制
- 实现APP与大屏的状态同步显示
- 照片状态显示在照片区域，视频状态显示在视频区域
- 工作人员可控制的演示开始和重置机制

#### v1.5 - 最终版本
- 6步完整进度时间线：APP连接→拍照→检测→生成→检测→分析
- 状态显示逻辑优化：相关状态显示在对应功能区域
- 展会现场适配：连续演示支持、异常处理、操作引导
