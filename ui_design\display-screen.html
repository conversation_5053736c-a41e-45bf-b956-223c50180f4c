<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSID - Defake深伪检测防御演示</title>
    
    <!-- External CSS -->
    <link href="https://cdn.staticfile.org/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            font-family: 'Noto Sans SC', <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, "Droid Sans", "Helvetica Neue", "Droid Sans Fallback", "Heiti SC", "Hiragino Sans GB", <PERSON><PERSON>, sans-serif;
        }

        body {
            background: linear-gradient(135deg, #0a0e1a 0%, #1a1f2e 50%, #0f172a 100%);
            overflow: hidden;
            min-height: 100vh;
        }

        .main-container {
            height: calc(100vh - 120px);
        }

        .glow-effect {
            box-shadow: 0 0 30px rgba(0, 102, 255, 0.3);
        }

        .pulse-glow {
            animation: pulseGlow 2s ease-in-out infinite alternate;
        }

        @keyframes pulseGlow {
            from { box-shadow: 0 0 20px rgba(0, 212, 255, 0.4); }
            to { box-shadow: 0 0 40px rgba(0, 212, 255, 0.8); }
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }

        .status-waiting { background-color: #fbbf24; }
        .status-processing { 
            background-color: #0066ff; 
            animation: pulse 1.5s infinite;
        }
        .status-complete { background-color: #10b981; }
        .status-error { background-color: #ef4444; }

        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.1); }
        }

        .progress-bar {
            background: linear-gradient(90deg, #0066ff, #00d4ff);
            transition: width 0.5s ease;
        }

        .video-container {
            background: radial-gradient(circle at center, #1e293b 0%, #0f172a 100%);
            border: 2px solid #334155;
        }

        .loading-dots {
            display: inline-flex;
            gap: 4px;
        }

        .loading-dots div {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #00d4ff;
            animation: loadingDots 1.4s ease-in-out infinite both;
        }

        .loading-dots div:nth-child(1) { animation-delay: -0.32s; }
        .loading-dots div:nth-child(2) { animation-delay: -0.16s; }

        @keyframes loadingDots {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
        }

        .scan-line {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, #00d4ff, transparent);
            animation: scan 4s linear infinite;
        }

        @keyframes scan {
            0% { transform: translateY(0); }
            100% { transform: translateY(426px); }
        }

        .floating-icon {
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .gradient-text {
            background: linear-gradient(135deg, #00d4ff, #0066ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .card-glow {
            background: rgba(26, 31, 46, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .detection-result {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 127, 0.1));
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .ai-explanation {
            background: linear-gradient(135deg, rgba(6, 182, 212, 0.1), rgba(59, 130, 246, 0.1));
            border: 1px solid rgba(6, 182, 212, 0.3);
        }

        .typewriter {
            overflow: hidden;
            border-right: 2px solid #00d4ff;
            white-space: nowrap;
            animation: typing 3s steps(40, end), blink-caret 0.75s step-end infinite;
        }

        @keyframes typing {
            from { width: 0; }
            to { width: 100%; }
        }

        @keyframes blink-caret {
            from, to { border-color: transparent; }
            50% { border-color: #00d4ff; }
        }
    </style>
</head>
<body class="text-white">
    <!-- Header -->
    <header class="px-8 py-6 border-b border-gray-700">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-400 rounded-xl flex items-center justify-center">
                    <i class="fas fa-shield-alt text-xl"></i>
                </div>
                <div>
                    <h1 class="text-3xl font-bold gradient-text">SSID - Defake深伪检测防御演示</h1>
                    <p class="text-gray-400">实时展示人工智能内容安全技术</p>
                </div>
            </div>
            <div class="flex items-center space-x-6">
                <div class="flex items-center space-x-2">
                    <div class="status-dot status-waiting" id="demoStatus"></div>
                    <span class="text-gray-400 font-medium" id="demoStatusText">等待开始演示</span>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="text-sm text-gray-400">
                        <span>演示次数: </span>
                        <span class="text-cyan-400 font-bold" id="demoCount">0</span>
                    </div>
                    <div class="text-right">
                        <div class="text-2xl font-bold text-white" id="currentTime"></div>
                        <div class="text-sm text-gray-400">当前时间</div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="px-8 py-6">
        <!-- Main Layout Grid -->
        <div class="grid grid-cols-12 gap-8 main-container">
            <!-- Left Column - Progress Timeline -->
            <div class="col-span-3 space-y-6">
                <!-- Progress Timeline -->
                <div class="card-glow rounded-2xl p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-bold text-white">
                            <i class="fas fa-timeline mr-2 text-orange-400"></i>
                            处理进度
                        </h3>
                        <i class="fas fa-cogs text-2xl text-purple-400 floating-icon"></i>
                    </div>
                    <div class="space-y-4" id="progressTimeline">
                        <div class="flex items-center space-x-3">
                            <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-check text-xs"></i>
                            </div>
                            <div class="flex-1">
                                <div class="text-white font-medium text-sm">人脸采集完成</div>
                                <div class="text-gray-400 text-xs">14:32:15</div>
                            </div>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-check text-xs"></i>
                            </div>
                            <div class="flex-1">
                                <div class="text-white font-medium text-sm">人脸检测完成</div>
                                <div class="text-gray-400 text-xs">14:32:18</div>
                            </div>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center pulse-glow">
                                <i class="fas fa-cog text-xs"></i>
                            </div>
                            <div class="flex-1">
                                <div class="text-white font-medium text-sm">正在生成视频</div>
                                <div class="flex items-center mt-1">
                                    <div class="w-16 bg-gray-600 rounded-full h-2 mr-2">
                                        <div class="progress-bar h-2 rounded-full" style="width: 0%" id="videoProgress"></div>
                                    </div>
                                    <span class="text-blue-400 text-xs" id="videoProgressText">0%</span>
                                </div>
                            </div>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-6 h-6 bg-gray-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-search text-xs"></i>
                            </div>
                            <div class="flex-1">
                                <div class="text-gray-500 font-medium text-sm">深伪检测</div>
                                <div class="text-gray-500 text-xs">等待中</div>
                            </div>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-6 h-6 bg-gray-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-brain text-xs"></i>
                            </div>
                            <div class="flex-1">
                                <div class="text-gray-500 font-medium text-sm">AI分析</div>
                                <div class="text-gray-500 text-xs">等待中</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Center Column - Original Photo & Video Comparison -->
            <div class="col-span-5 flex flex-col">
                <div class="card-glow rounded-2xl p-6 flex-1">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-xl font-bold text-white">
                            <i class="fas fa-exchange-alt mr-2 text-green-400"></i>
                            原图与合成视频对比
                        </h3>
                        <div class="flex items-center space-x-4">
                            <button id="startDemoBtn" class="px-6 py-3 bg-green-600 hover:bg-green-700 rounded-lg transition-colors text-sm font-medium">
                                <i class="fas fa-play mr-2"></i>开始演示
                            </button>
                            <button id="resetDemoBtn" class="px-6 py-3 bg-orange-600 hover:bg-orange-700 rounded-lg transition-colors text-sm font-medium">
                                <i class="fas fa-redo mr-2"></i>重置演示
                            </button>
                            <button id="downloadBtn" class="px-4 py-3 bg-gray-600 hover:bg-gray-700 rounded-lg transition-colors text-sm hidden">
                                <i class="fas fa-download mr-2"></i>下载
                            </button>
                        </div>
                    </div>
                    <!-- 原图与视频对比容器 -->
                    <div class="flex justify-center items-center h-full space-x-8">
                        <!-- 原始照片 -->
                        <div class="flex flex-col items-center">
                            <h4 class="text-lg font-medium text-gray-300 mb-3">原始照片</h4>
                            <div class="bg-gray-800 rounded-xl flex items-center justify-center relative overflow-hidden"
                                 style="width: 240px; height: 426px;" id="originalPhotoContainer">
                                <i class="fas fa-user text-5xl text-gray-600" id="originalPhotoPlaceholder"></i>
                                <img id="originalPhoto" class="w-full h-full object-cover rounded-xl hidden" />
                            </div>
                        </div>

                        <!-- 箭头指示 -->
                        <div class="flex flex-col items-center">
                            <i class="fas fa-arrow-right text-3xl text-cyan-400 floating-icon"></i>
                            <span class="text-sm text-gray-400 mt-3 font-medium">AI生成</span>
                        </div>

                        <!-- 合成视频 -->
                        <div class="flex flex-col items-center">
                            <h4 class="text-lg font-medium text-gray-300 mb-3">合成视频</h4>
                            <div class="video-container rounded-xl flex items-center justify-center relative overflow-hidden"
                                 style="width: 240px; height: 426px;" id="videoContainer">
                                <div class="text-center text-gray-400" id="videoPlaceholder">
                                    <i class="fas fa-video text-4xl mb-4"></i>
                                    <p class="text-base mb-2">等待开始</p>
                                    <p class="text-sm mb-4">请点击开始演示</p>
                                </div>
                                <video id="generatedVideo" class="w-full h-full object-cover rounded-xl hidden" controls>
                                    <source src="" type="video/mp4">
                                </video>
                                <div class="scan-line" id="scanLine"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column - Detection Results & AI Analysis -->
            <div class="col-span-4 space-y-4">
                <!-- Detection Results -->
                <div class="detection-result rounded-2xl p-4">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="text-lg font-bold text-white">深伪检测结果</h3>
                        <i class="fas fa-search text-xl text-red-400 floating-icon"></i>
                    </div>
                    <div class="text-center" id="detectionResultContainer">
                        <div class="text-3xl mb-3">
                            <i class="fas fa-hourglass-half text-yellow-400 pulse-glow"></i>
                        </div>
                        <p class="text-gray-400 text-sm">等待检测完成...</p>
                        <div class="mt-3">
                            <div class="text-2xl font-bold text-red-400 hidden" id="detectionScore">95.7%</div>
                            <div class="text-sm text-gray-400 hidden" id="detectionLabel">检测为合成内容</div>
                        </div>
                    </div>
                </div>

                <!-- AI Analysis Output - 增加显示占比 -->
                <div class="ai-explanation rounded-2xl p-6 flex-1" style="min-height: 400px;">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-xl font-bold text-white">
                            <i class="fas fa-brain mr-2 text-cyan-400"></i>
                            大模型AI分析
                        </h3>
                        <div class="flex items-center space-x-2">
                            <div class="status-dot status-waiting" id="aiAnalysisStatus"></div>
                            <span class="text-sm text-gray-400" id="aiAnalysisStatusText">等待分析</span>
                        </div>
                    </div>
                    <div class="bg-gray-800 bg-opacity-50 rounded-xl p-4 h-80 overflow-y-auto">
                        <p class="text-gray-400 text-sm leading-relaxed" id="aiExplanation">
                            检测分析将在视频生成完成后开始，AI将从多个维度分析视频的真实性，并提供详细的技术解释...

                            分析维度包括：
                            • 面部特征一致性检测
                            • 光照与阴影合理性分析
                            • 边缘模糊度与自然度评估
                            • 时序连贯性与运动轨迹分析
                            • 纹理细节与皮肤质感检测
                            • 眼部运动与眨眼频率分析
                            • 嘴唇同步性与发音匹配度
                            • 整体视觉一致性评估

                            AI模型将综合以上多个维度的检测结果，给出最终的真实性判断和置信度评分。
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script>
        // 实时时间显示
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('zh-CN', { 
                hour12: false,
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('currentTime').textContent = timeString;
        }
        
        setInterval(updateTime, 1000);
        updateTime();

        // 模拟演示流程
        class DemoSimulator {
            constructor() {
                this.currentStep = 0;
                this.isRunning = false;
                this.demoCount = 0;
                this.steps = [
                    { name: 'photoUpload', duration: 3000 },
                    { name: 'faceDetection', duration: 2000 },
                    { name: 'featureExtraction', duration: 3000 },
                    { name: 'videoGeneration', duration: 15000 },
                    { name: 'deepfakeDetection', duration: 8000 },
                    { name: 'aiExplanation', duration: 5000 }
                ];
                this.initControls();
            }

            initControls() {
                const startBtn = document.getElementById('startDemoBtn');
                const resetBtn = document.getElementById('resetDemoBtn');

                startBtn.addEventListener('click', () => {
                    if (!this.isRunning) {
                        this.start();
                    }
                });

                resetBtn.addEventListener('click', () => {
                    this.reset();
                });
            }

            start() {
                if (this.isRunning) return;

                this.isRunning = true;
                this.demoCount++;
                this.updateDemoStatus('processing', '演示进行中');
                this.updateDemoCount();
                this.updateStartButton(false);

                this.executeStep(0);
            }

            reset() {
                this.isRunning = false;
                this.currentStep = 0;
                this.updateDemoStatus('waiting', '等待开始演示');
                this.updateStartButton(true);
                this.resetUI();
            }

            updateDemoStatus(status, text) {
                const statusDot = document.getElementById('demoStatus');
                const statusText = document.getElementById('demoStatusText');

                statusDot.className = `status-dot status-${status}`;
                statusText.textContent = text;
            }

            updateDemoCount() {
                document.getElementById('demoCount').textContent = this.demoCount;
            }

            updateStartButton(enabled) {
                const startBtn = document.getElementById('startDemoBtn');
                if (enabled) {
                    startBtn.disabled = false;
                    startBtn.className = 'px-6 py-3 bg-green-600 hover:bg-green-700 rounded-lg transition-colors text-sm font-medium';
                    startBtn.innerHTML = '<i class="fas fa-play mr-2"></i>开始演示';
                } else {
                    startBtn.disabled = true;
                    startBtn.className = 'px-6 py-3 bg-gray-600 rounded-lg transition-colors text-sm font-medium cursor-not-allowed';
                    startBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>演示中...';
                }
            }

            executeStep(stepIndex) {
                if (stepIndex >= this.steps.length) {
                    this.onDemoComplete();
                    return;
                }

                const step = this.steps[stepIndex];

                switch(step.name) {
                    case 'photoUpload':
                        this.simulatePhotoUpload();
                        break;
                    case 'faceDetection':
                        this.simulateFaceDetection();
                        break;
                    case 'featureExtraction':
                        this.simulateFeatureExtraction();
                        break;
                    case 'videoGeneration':
                        this.simulateVideoGeneration();
                        break;
                    case 'deepfakeDetection':
                        this.simulateDeepfakeDetection();
                        break;
                    case 'aiExplanation':
                        this.simulateAIExplanation();
                        break;
                }

                setTimeout(() => {
                    this.executeStep(stepIndex + 1);
                }, step.duration);
            }

            onDemoComplete() {
                this.isRunning = false;
                this.updateDemoStatus('complete', '演示完成');
                this.updateStartButton(true);
                document.getElementById('downloadBtn').classList.remove('hidden');

                // 10秒后自动重置，准备下一次演示
                setTimeout(() => {
                    if (!this.isRunning) {
                        this.reset();
                        document.getElementById('downloadBtn').classList.add('hidden');
                    }
                }, 10000);
            }

            resetUI() {
                // 重置原始照片
                const originalPhotoPlaceholder = document.getElementById('originalPhotoPlaceholder');
                originalPhotoPlaceholder.className = 'fas fa-user text-5xl text-gray-600';

                // 重置视频区域
                const videoPlaceholder = document.getElementById('videoPlaceholder');
                videoPlaceholder.innerHTML = `
                    <i class="fas fa-video text-4xl mb-4"></i>
                    <p class="text-base mb-2">等待开始</p>
                    <p class="text-sm mb-4">请点击开始演示</p>
                `;

                // 重置进度条
                document.getElementById('videoProgress').style.width = '0%';
                document.getElementById('videoProgressText').textContent = '0%';

                // 重置检测结果
                const detectionContainer = document.getElementById('detectionResultContainer');
                detectionContainer.innerHTML = `
                    <div class="text-3xl mb-3">
                        <i class="fas fa-hourglass-half text-gray-400"></i>
                    </div>
                    <p class="text-gray-400 text-sm">等待检测开始...</p>
                `;

                // 重置AI分析
                const explanation = document.getElementById('aiExplanation');
                const aiAnalysisStatus = document.getElementById('aiAnalysisStatus');
                const aiAnalysisStatusText = document.getElementById('aiAnalysisStatusText');

                explanation.className = 'text-gray-400 text-sm leading-relaxed';
                explanation.textContent = '检测分析将在视频生成完成后开始，AI将从多个维度分析视频的真实性，并提供详细的技术解释...';
                aiAnalysisStatus.className = 'status-dot status-waiting';
                aiAnalysisStatusText.textContent = '等待分析';

                // 重置进度时间线
                this.resetProgressTimeline();

                // 隐藏下载按钮
                document.getElementById('downloadBtn').classList.add('hidden');
            }

            resetProgressTimeline() {
                const timeline = document.getElementById('progressTimeline');
                const steps = timeline.children;

                // 重置所有步骤为初始状态
                for (let i = 0; i < steps.length; i++) {
                    const circle = steps[i].querySelector('.w-6');
                    const statusText = steps[i].querySelector('.text-white, .text-gray-500');
                    const timeText = steps[i].querySelector('.text-gray-400, .text-xs');

                    if (i === 0) {
                        // 第一步保持完成状态
                        circle.className = 'w-6 h-6 bg-green-500 rounded-full flex items-center justify-center';
                        circle.innerHTML = '<i class="fas fa-check text-xs"></i>';
                        statusText.className = 'text-white font-medium text-sm';
                        timeText.textContent = '等待开始';
                    } else {
                        circle.className = 'w-6 h-6 bg-gray-600 rounded-full flex items-center justify-center';
                        statusText.className = 'text-gray-500 font-medium text-sm';
                        timeText.textContent = '等待中';

                        // 设置对应的图标
                        if (i === 1) circle.innerHTML = '<i class="fas fa-search text-xs"></i>';
                        else if (i === 2) circle.innerHTML = '<i class="fas fa-cog text-xs"></i>';
                        else if (i === 3) circle.innerHTML = '<i class="fas fa-search text-xs"></i>';
                        else if (i === 4) circle.innerHTML = '<i class="fas fa-brain text-xs"></i>';
                    }
                }
            }

            simulatePhotoUpload() {
                const originalPhotoPlaceholder = document.getElementById('originalPhotoPlaceholder');

                // 显示原始照片
                setTimeout(() => {
                    originalPhotoPlaceholder.className = 'fas fa-check text-3xl text-green-400';
                    // 这里可以设置实际的照片
                    // document.getElementById('originalPhoto').src = 'path/to/photo.jpg';
                    // document.getElementById('originalPhoto').classList.remove('hidden');
                    // originalPhotoPlaceholder.classList.add('hidden');
                }, 2000);
            }

            simulateFaceDetection() {
                // 更新进度时间线中的人脸检测状态
                this.updateProgressStep(1, 'processing');

                setTimeout(() => {
                    this.updateProgressStep(1, 'complete');
                }, 1500);
            }

            simulateFeatureExtraction() {
                // 更新进度时间线中的特征提取状态
                this.updateProgressStep(2, 'processing');

                setTimeout(() => {
                    this.updateProgressStep(2, 'complete');
                }, 2500);
            }

            updateProgressStep(stepIndex, status) {
                const timeline = document.getElementById('progressTimeline');
                const steps = timeline.children;

                if (steps[stepIndex]) {
                    const circle = steps[stepIndex].querySelector('.w-6');
                    const statusText = steps[stepIndex].querySelector('.text-gray-500, .text-white');
                    const timeText = steps[stepIndex].querySelector('.text-gray-400, .text-xs');

                    if (status === 'processing') {
                        circle.className = 'w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center pulse-glow';
                        circle.innerHTML = '<i class="fas fa-cog text-xs"></i>';
                        statusText.className = 'text-white font-medium text-sm';
                        timeText.textContent = '进行中...';
                    } else if (status === 'complete') {
                        circle.className = 'w-6 h-6 bg-green-500 rounded-full flex items-center justify-center';
                        circle.innerHTML = '<i class="fas fa-check text-xs"></i>';
                        statusText.className = 'text-white font-medium text-sm';
                        const now = new Date();
                        timeText.textContent = now.toLocaleTimeString('zh-CN', { hour12: false });
                    }
                }
            }

            simulateVideoGeneration() {
                const progressBar = document.getElementById('videoProgress');
                const progressText = document.getElementById('videoProgressText');
                const scanLine = document.getElementById('scanLine');

                // 更新进度时间线状态
                this.updateProgressStep(2, 'processing');

                scanLine.style.display = 'block';

                let progress = 0;
                const interval = setInterval(() => {
                    progress += Math.random() * 8;
                    if (progress > 100) progress = 100;

                    progressBar.style.width = progress + '%';
                    progressText.textContent = Math.round(progress) + '%';

                    if (progress >= 100) {
                        clearInterval(interval);
                        scanLine.style.display = 'none';
                        this.updateProgressStep(2, 'complete');
                        this.showGeneratedVideo();
                    }
                }, 500);
            }

            showGeneratedVideo() {
                const videoPlaceholder = document.getElementById('videoPlaceholder');
                const videoContainer = document.getElementById('videoContainer');
                
                videoPlaceholder.innerHTML = `
                    <div class="text-center text-green-400">
                        <i class="fas fa-check-circle text-6xl mb-4"></i>
                        <p class="text-lg">视频生成完成</p>
                        <button class="mt-4 px-6 py-2 bg-green-600 hover:bg-green-700 rounded-lg transition-colors">
                            <i class="fas fa-play mr-2"></i>播放视频
                        </button>
                    </div>
                `;
            }

            simulateDeepfakeDetection() {
                const detectionContainer = document.getElementById('detectionResultContainer');

                // 更新进度时间线状态
                this.updateProgressStep(3, 'processing');

                setTimeout(() => {
                    this.updateProgressStep(3, 'complete');
                    detectionContainer.innerHTML = `
                        <div class="text-center">
                            <div class="text-4xl mb-3">
                                <i class="fas fa-exclamation-triangle text-red-400"></i>
                            </div>
                            <div class="text-2xl font-bold text-red-400 mb-2">95.7%</div>
                            <div class="text-sm text-gray-300">检测为合成内容</div>
                        </div>
                    `;
                }, 6000);
            }

            simulateAIExplanation() {
                const explanation = document.getElementById('aiExplanation');
                const aiAnalysisStatus = document.getElementById('aiAnalysisStatus');
                const aiAnalysisStatusText = document.getElementById('aiAnalysisStatusText');

                // 更新进度时间线状态
                this.updateProgressStep(4, 'processing');

                // 更新状态为处理中
                aiAnalysisStatus.className = 'status-dot status-processing';
                aiAnalysisStatusText.textContent = '分析中';

                const explanationText = `基于深度学习模型的综合分析结果：

经过多维度技术检测，该视频存在明显的人工合成特征。检测算法通过分析视频帧间的连续性、面部特征的自然度、以及整体视觉一致性等关键指标，确定该内容为AI生成的深伪视频。

【详细分析维度】

1. 面部特征一致性检测
   - 检测到面部轮廓与原始照片存在细微差异
   - 眼部区域的纹理细节不够自然
   - 鼻梁阴影与光照方向不完全匹配

2. 光照与阴影合理性分析
   - 面部光照分布存在不自然的渐变
   - 左侧脸颊阴影过于锐利，缺乏自然过渡
   - 眼窝阴影深度与环境光照不符

3. 边缘模糊度与自然度评估
   - 发际线边缘存在明显的人工处理痕迹
   - 面部与背景交界处模糊度异常
   - 耳朵轮廓细节缺失

4. 时序连贯性与运动轨迹分析
   - 面部表情变化过于机械化
   - 眼球运动轨迹不符合自然规律
   - 头部转动时颈部肌肉变化不自然

5. 纹理细节与皮肤质感检测
   - 皮肤纹理过于平滑，缺乏真实的毛孔细节
   - 皮肤反光度异常，不符合真实人脸特征
   - 面部微表情缺乏真实性

【最终判定】
置信度：95.7%
风险等级：高风险
建议处理：标记为合成媒体内容

检测技术基于先进的深度神经网络架构，能够有效识别当前主流的深伪生成技术产生的合成内容。该检测结果具有较高的可信度，建议对此类内容进行相应的标记和处理。`;

                explanation.className = 'text-cyan-300 text-sm leading-relaxed';
                explanation.textContent = '';

                let i = 0;
                const typeInterval = setInterval(() => {
                    if (i < explanationText.length) {
                        explanation.textContent += explanationText.charAt(i);
                        i++;
                        // 自动滚动到底部
                        explanation.parentElement.scrollTop = explanation.parentElement.scrollHeight;
                    } else {
                        clearInterval(typeInterval);
                        // 更新状态为完成
                        this.updateProgressStep(4, 'complete');
                        aiAnalysisStatus.className = 'status-dot status-complete';
                        aiAnalysisStatusText.textContent = '分析完成';
                    }
                }, 20);
            }
        }

        // 启动演示控制器
        const simulator = new DemoSimulator();

        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            // 不自动开始，等待用户点击
            console.log('演示系统已就绪，等待用户操作');
        });

        // 添加一些交互效果
        document.addEventListener('mousemove', (e) => {
            const cards = document.querySelectorAll('.card-glow');
            cards.forEach(card => {
                const rect = card.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                if (x >= 0 && x <= rect.width && y >= 0 && y <= rect.height) {
                    card.style.background = `radial-gradient(circle at ${x}px ${y}px, rgba(0, 212, 255, 0.1), rgba(26, 31, 46, 0.8))`;
                } else {
                    card.style.background = 'rgba(26, 31, 46, 0.8)';
                }
            });
        });
    </script>
</body>
</html>
