from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, Form
from fastapi.responses import FileResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import shutil
import os
import uuid
import subprocess
from typing import List

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

UPLOAD_DIR = "uploads"
OUTPUT_DIR = "outputs"
DRIVING_DIR = os.path.join(os.path.dirname(__file__), "..", "LivePortrait-main", "assets", "examples", "driving")

os.makedirs(UPLOAD_DIR, exist_ok=True)
os.makedirs(OUTPUT_DIR, exist_ok=True)

def get_driving_pkl(action: str) -> str:
    mapping = {
        "点头": "d0.pkl",
        "摇头": "shake_face.pkl",
        "张嘴": "open_lip.pkl",
        "眨眼": "wink.pkl"
    }
    filename = mapping.get(action)
    if not filename:
        raise ValueError(f"不支持的动作: {action}")
    return os.path.join(DRIVING_DIR, filename)

@app.post("/generate_video/")
async def generate_video(
    image: UploadFile = File(...),
    actions: List[str] = Form(...)
):
    # 保存上传图片
    image_id = str(uuid.uuid4())
    image_path = os.path.join(UPLOAD_DIR, f"{image_id}.png")
    with open(image_path, "wb") as buffer:
        shutil.copyfileobj(image.file, buffer)

    video_paths = []
    for action in actions:
        try:
            driving_pkl = get_driving_pkl(action)
        except Exception as e:
            return JSONResponse(status_code=400, content={"error": str(e)})
        output_video = os.path.join(OUTPUT_DIR, f"{image_id}_{action}.mp4")
        # 调用LivePortrait-main推理脚本
        cmd = [
            "python", os.path.join(os.path.dirname(__file__), "..", "LivePortrait-main", "inference.py"),
            "--source_image", image_path,
            "--driving_video", driving_pkl,
            "--output", output_video,
            "--relative",
            "--no_audio"
        ]
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        except subprocess.CalledProcessError as e:
            return JSONResponse(status_code=500, content={"error": f"LivePortrait生成失败: {e.stderr}"})
        video_paths.append(output_video)

    return {"videos": video_paths}