<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>选择方式页面</title>
    <link href="https://cdn.staticfile.org/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
<link href="https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        ::-webkit-scrollbar { display: none; }
        body { -ms-overflow-style: none; scrollbar-width: none; }
        .phone-mockup { width: 375px; height: 812px; background: #000; border-radius: 40px; padding: 8px; margin: 20px auto; }
        .phone-screen { width: 100%; height: 100%; background: #f7fafc; border-radius: 32px; overflow: hidden; position: relative; }
        .card-hover { transition: all 0.3s ease; }
        .card-hover:hover { transform: translateY(-5px); box-shadow: 0 10px 20px rgba(0,0,0,0.05); }
    </style>
</head>
<body class="bg-gray-100">
    <div class="phone-mockup">
        <div class="phone-screen bg-gradient-to-br from-blue-50 via-cyan-50 to-purple-100 flex flex-col p-0">
            <!-- Header -->
            <div class="flex items-center px-6 py-4 mb-6">
                <button class="text-blue-400 hover:text-blue-600 transition-colors"><i class="fas fa-arrow-left"></i></button>
                <h1 class="text-2xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-blue-500 via-cyan-500 to-purple-500 mx-auto tracking-wide">选择采集方式</h1>
            </div>
            <!-- Options -->
            <div class="flex-grow flex flex-col justify-center items-center space-y-8 px-6">
                <div class="bg-white rounded-3xl p-8 text-center shadow-xl hover:shadow-2xl transition-shadow duration-300 w-full max-w-xs cursor-pointer transform hover:scale-105">
                    <div class="w-20 h-20 bg-gradient-to-br from-blue-200 via-cyan-200 to-purple-200 text-blue-600 rounded-full flex items-center justify-center mx-auto mb-6 shadow-md">
                        <i class="fas fa-camera text-3xl"></i>
                    </div>
                    <h2 class="text-xl font-bold text-gray-800 mb-2">拍照采集</h2>
                    <p class="text-base text-gray-500">使用摄像头拍摄一张新照片</p>
                </div>
                <div class="bg-white rounded-3xl p-8 text-center shadow-xl hover:shadow-2xl transition-shadow duration-300 w-full max-w-xs cursor-pointer transform hover:scale-105">
                    <div class="w-20 h-20 bg-gradient-to-br from-purple-200 via-cyan-200 to-blue-200 text-purple-600 rounded-full flex items-center justify-center mx-auto mb-6 shadow-md">
                        <i class="fas fa-images text-3xl"></i>
                    </div>
                    <h2 class="text-xl font-bold text-gray-800 mb-2">预设照片</h2>
                    <p class="text-base text-gray-500">从照片库中选择一张照片</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>