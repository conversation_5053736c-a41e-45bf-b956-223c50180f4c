<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>上传处理页面</title>
    <link href="https://cdn.staticfile.org/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
<link href="https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        ::-webkit-scrollbar { display: none; }
        body { -ms-overflow-style: none; scrollbar-width: none; }
        .phone-mockup { width: 375px; height: 812px; background: #000; border-radius: 40px; padding: 8px; margin: 20px auto; }
        .phone-screen { width: 100%; height: 100%; background: #f7fafc; border-radius: 32px; overflow: hidden; position: relative; }
        .progress-ring__circle { transition: stroke-dashoffset 0.5s; transform: rotate(-90deg); transform-origin: 50% 50%; }
        @keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: 0.7; } }
        .pulse-animation { animation: pulse 2s infinite; }
    </style>
</head>
<body class="bg-gray-100">
    <div class="phone-mockup">
        <div class="phone-screen bg-gradient-to-br from-blue-50 via-cyan-50 to-purple-100 flex flex-col p-0">
            <!-- Header -->
            <div class="flex items-center px-6 py-4 mb-6">
                <h1 class="text-2xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-blue-500 via-cyan-500 to-purple-500 mx-auto tracking-wide">上传处理中</h1>
            </div>
            <!-- Progress Circle -->
            <div class="flex flex-col items-center justify-center flex-grow mb-8">
                <div class="relative w-36 h-36 mb-6">
                    <svg class="absolute top-0 left-0" width="144" height="144">
                        <circle cx="72" cy="72" r="62" stroke="#e0e7ef" stroke-width="12" fill="none" />
                        <circle cx="72" cy="72" r="62" stroke="url(#progressGradient)" stroke-width="12" fill="none" stroke-dasharray="389.6" stroke-dashoffset="97.4" stroke-linecap="round" />
                        <defs>
                            <linearGradient id="progressGradient" x1="0" y1="0" x2="144" y2="144" gradientUnits="userSpaceOnUse">
                                <stop stop-color="#3b82f6" />
                                <stop offset="0.5" stop-color="#06b6d4" />
                                <stop offset="1" stop-color="#a78bfa" />
                            </linearGradient>
                        </defs>
                    </svg>
                    <div class="absolute inset-0 flex items-center justify-center">
                        <span class="text-4xl font-extrabold text-blue-500 animate-pulse">75%</span>
                    </div>
                </div>
                <div class="text-lg text-gray-700 font-semibold mb-2">正在处理照片...</div>
                <div class="text-base text-gray-500">AI正在分析您的照片，请稍候</div>
            </div>
            <!-- Animation Box -->
            <div class="bg-gradient-to-r from-blue-100 via-cyan-100 to-purple-100 rounded-2xl p-5 flex items-center shadow-xl mx-6 mb-8 animate-fade-in">
                <i class="fas fa-tv text-blue-400 text-2xl mr-4 animate-pulse"></i>
                <span class="text-blue-500 font-bold">请关注大屏幕查看检测结果</span>
            </div>
        </div>
    </div>
</body>
</html>